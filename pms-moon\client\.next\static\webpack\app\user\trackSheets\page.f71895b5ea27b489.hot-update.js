"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/user/trackSheets/page",{

/***/ "(app-pages-browser)/./app/user/trackSheets/createTrackSheet.tsx":
/*!***************************************************!*\
  !*** ./app/user/trackSheets/createTrackSheet.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(app-pages-browser)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var _components_ui_form__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/form */ \"(app-pages-browser)/./components/ui/form.tsx\");\n/* harmony import */ var _app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/_component/FormInput */ \"(app-pages-browser)/./app/_component/FormInput.tsx\");\n/* harmony import */ var _app_component_FormCheckboxGroup__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/_component/FormCheckboxGroup */ \"(app-pages-browser)/./app/_component/FormCheckboxGroup.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,DollarSign,FileText,Hash,Info,MinusCircle,PlusCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,DollarSign,FileText,Hash,Info,MinusCircle,PlusCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,DollarSign,FileText,Hash,Info,MinusCircle,PlusCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,DollarSign,FileText,Hash,Info,MinusCircle,PlusCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,DollarSign,FileText,Hash,Info,MinusCircle,PlusCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/hash.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,DollarSign,FileText,Hash,Info,MinusCircle,PlusCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-minus.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,DollarSign,FileText,Hash,Info,MinusCircle,PlusCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-plus.js\");\n/* harmony import */ var _lib_helpers__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/helpers */ \"(app-pages-browser)/./lib/helpers.ts\");\n/* harmony import */ var _lib_routePath__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/routePath */ \"(app-pages-browser)/./lib/routePath.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! zod */ \"(app-pages-browser)/./node_modules/zod/lib/index.mjs\");\n/* harmony import */ var _app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/_component/SearchSelect */ \"(app-pages-browser)/./app/_component/SearchSelect.tsx\");\n/* harmony import */ var _app_component_PageInput__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/app/_component/PageInput */ \"(app-pages-browser)/./app/_component/PageInput.tsx\");\n/* harmony import */ var _components_ui_tooltip__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/tooltip */ \"(app-pages-browser)/./components/ui/tooltip.tsx\");\n/* harmony import */ var _LegrandDetailsComponent__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./LegrandDetailsComponent */ \"(app-pages-browser)/./app/user/trackSheets/LegrandDetailsComponent.tsx\");\n/* harmony import */ var _ClientSelectPage__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./ClientSelectPage */ \"(app-pages-browser)/./app/user/trackSheets/ClientSelectPage.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst validateFtpPageFormat = (value)=>{\n    if (!value || value.trim() === \"\") return false;\n    const ftpPageRegex = /^(\\d+)\\s+of\\s+(\\d+)$/i;\n    const match = value.match(ftpPageRegex);\n    if (!match) return false;\n    const currentPage = parseInt(match[1], 10);\n    const totalPages = parseInt(match[2], 10);\n    return currentPage > 0 && totalPages > 0 && currentPage <= totalPages;\n};\nconst trackSheetSchema = zod__WEBPACK_IMPORTED_MODULE_16__.z.object({\n    clientId: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().min(1, \"Client is required\"),\n    entries: zod__WEBPACK_IMPORTED_MODULE_16__.z.array(zod__WEBPACK_IMPORTED_MODULE_16__.z.object({\n        company: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().min(1, \"Company is required\"),\n        division: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        invoice: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().min(1, \"Invoice is required\"),\n        masterInvoice: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        bol: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        invoiceDate: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().min(1, \"Invoice date is required\"),\n        receivedDate: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().min(1, \"Received date is required\"),\n        shipmentDate: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        carrierName: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().min(1, \"Carrier name is required\"),\n        invoiceStatus: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().min(1, \"Invoice status is required\"),\n        manualMatching: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().min(1, \"Manual matching is required\"),\n        invoiceType: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().min(1, \"Invoice type is required\"),\n        billToClient: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        currency: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().min(1, \"Currency is required\"),\n        qtyShipped: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        weightUnitName: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().min(1, \"Weight unit is required\"),\n        quantityBilledText: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        invoiceTotal: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().min(1, \"Invoice total is required\"),\n        savings: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        financialNotes: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        ftpFileName: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().min(1, \"FTP File Name is required\"),\n        ftpPage: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().min(1, \"FTP Page is required\").refine((value)=>validateFtpPageFormat(value), (value)=>{\n            if (!value || value.trim() === \"\") {\n                return {\n                    message: \"FTP Page is required\"\n                };\n            }\n            const ftpPageRegex = /^(\\d+)\\s+of\\s+(\\d+)$/i;\n            const match = value.match(ftpPageRegex);\n            if (!match) {\n                return {\n                    message: \"\"\n                };\n            }\n            const currentPage = parseInt(match[1], 10);\n            const totalPages = parseInt(match[2], 10);\n            if (currentPage <= 0 || totalPages <= 0) {\n                return {\n                    message: \"Page numbers must be positive (greater than 0)\"\n                };\n            }\n            if (currentPage > totalPages) {\n                return {\n                    message: \"Please enter a page number between \".concat(totalPages, \" and \").concat(currentPage, \" \")\n                };\n            }\n            return {\n                message: \"Invalid page format\"\n            };\n        }),\n        docAvailable: zod__WEBPACK_IMPORTED_MODULE_16__.z.array(zod__WEBPACK_IMPORTED_MODULE_16__.z.string()).optional().default([]),\n        otherDocuments: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        notes: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        //mistake: z.string().optional(),\n        legrandAlias: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        legrandCompanyName: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        legrandAddress: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        legrandZipcode: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        shipperAlias: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        shipperAddress: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        shipperZipcode: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        consigneeAlias: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        consigneeAddress: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        consigneeZipcode: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        billtoAlias: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        billtoAddress: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        billtoZipcode: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        customFields: zod__WEBPACK_IMPORTED_MODULE_16__.z.array(zod__WEBPACK_IMPORTED_MODULE_16__.z.object({\n            id: zod__WEBPACK_IMPORTED_MODULE_16__.z.string(),\n            name: zod__WEBPACK_IMPORTED_MODULE_16__.z.string(),\n            type: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n            value: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional()\n        })).default([])\n    }))\n});\nconst CreateTrackSheet = (param)=>{\n    let { client, carrier, associate, userData, activeView, setActiveView, permissions, carrierDataUpdate, clientDataUpdate } = param;\n    _s();\n    const companyFieldRefs = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)([]);\n    const [generatedFilenames, setGeneratedFilenames] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filenameValidation, setFilenameValidation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [missingFields, setMissingFields] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [legrandData, setLegrandData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [manualMatchingData, setManualMatchingData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [customFieldsRefresh, setCustomFieldsRefresh] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [showFullForm, setShowFullForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [initialAssociateId, setInitialAssociateId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [initialClientId, setInitialClientId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const selectionForm = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_17__.useForm)({\n        defaultValues: {\n            associateId: \"\",\n            clientId: \"\"\n        }\n    });\n    const associateOptions = associate === null || associate === void 0 ? void 0 : associate.map((a)=>{\n        var _a_id;\n        return {\n            value: (_a_id = a.id) === null || _a_id === void 0 ? void 0 : _a_id.toString(),\n            label: a.name,\n            name: a.name\n        };\n    });\n    const carrierOptions = carrier === null || carrier === void 0 ? void 0 : carrier.map((c)=>{\n        var _c_id;\n        return {\n            value: (_c_id = c.id) === null || _c_id === void 0 ? void 0 : _c_id.toString(),\n            label: c.name\n        };\n    });\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter)();\n    const form = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_17__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__.zodResolver)(trackSheetSchema),\n        defaultValues: {\n            associateId: \"\",\n            clientId: \"\",\n            entries: [\n                {\n                    company: \"\",\n                    division: \"\",\n                    invoice: \"\",\n                    masterInvoice: \"\",\n                    bol: \"\",\n                    invoiceDate: \"\",\n                    receivedDate: \"\",\n                    shipmentDate: \"\",\n                    carrierName: \"\",\n                    invoiceStatus: \"ENTRY\",\n                    manualMatching: \"\",\n                    invoiceType: \"\",\n                    billToClient: \"\",\n                    currency: \"\",\n                    qtyShipped: \"\",\n                    weightUnitName: \"\",\n                    quantityBilledText: \"\",\n                    invoiceTotal: \"\",\n                    savings: \"\",\n                    financialNotes: \"\",\n                    ftpFileName: \"\",\n                    ftpPage: \"\",\n                    docAvailable: [],\n                    otherDocuments: \"\",\n                    notes: \"\",\n                    //mistake: \"\",\n                    legrandAlias: \"\",\n                    legrandCompanyName: \"\",\n                    legrandAddress: \"\",\n                    legrandZipcode: \"\",\n                    shipperAlias: \"\",\n                    shipperAddress: \"\",\n                    shipperZipcode: \"\",\n                    consigneeAlias: \"\",\n                    consigneeAddress: \"\",\n                    consigneeZipcode: \"\",\n                    billtoAlias: \"\",\n                    billtoAddress: \"\",\n                    billtoZipcode: \"\",\n                    customFields: []\n                }\n            ]\n        }\n    });\n    const getFilteredClientOptions = ()=>{\n        if (!initialAssociateId) {\n            return (client === null || client === void 0 ? void 0 : client.map((c)=>{\n                var _c_id;\n                return {\n                    value: (_c_id = c.id) === null || _c_id === void 0 ? void 0 : _c_id.toString(),\n                    label: c.client_name,\n                    name: c.client_name\n                };\n            })) || [];\n        }\n        const filteredClients = (client === null || client === void 0 ? void 0 : client.filter((c)=>{\n            var _c_associateId;\n            return ((_c_associateId = c.associateId) === null || _c_associateId === void 0 ? void 0 : _c_associateId.toString()) === initialAssociateId;\n        })) || [];\n        return filteredClients.map((c)=>{\n            var _c_id;\n            return {\n                value: (_c_id = c.id) === null || _c_id === void 0 ? void 0 : _c_id.toString(),\n                label: c.client_name,\n                name: c.client_name\n            };\n        });\n    };\n    const clientOptions = getFilteredClientOptions();\n    const entries = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_17__.useWatch)({\n        control: form.control,\n        name: \"entries\"\n    });\n    const validateClientForAssociate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((associateId, currentClientId)=>{\n        if (associateId && currentClientId) {\n            var _currentClient_associateId;\n            const currentClient = client === null || client === void 0 ? void 0 : client.find((c)=>{\n                var _c_id;\n                return ((_c_id = c.id) === null || _c_id === void 0 ? void 0 : _c_id.toString()) === currentClientId;\n            });\n            if (currentClient && ((_currentClient_associateId = currentClient.associateId) === null || _currentClient_associateId === void 0 ? void 0 : _currentClient_associateId.toString()) !== associateId) {\n                form.setValue(\"clientId\", \"\");\n                setInitialClientId(\"\");\n                return false;\n            }\n        }\n        return true;\n    }, [\n        client,\n        form\n    ]);\n    const clearEntrySpecificClients = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        const currentEntries = form.getValues(\"entries\") || [];\n        if (currentEntries.length > 0) {\n            const hasEntrySpecificClients = currentEntries.some((entry)=>entry.clientId);\n            if (hasEntrySpecificClients) {\n                const updatedEntries = currentEntries.map((entry)=>({\n                        ...entry,\n                        clientId: \"\"\n                    }));\n                form.setValue(\"entries\", updatedEntries);\n            }\n        }\n    }, [\n        form\n    ]);\n    const fetchLegrandData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        try {\n            const response = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_7__.getAllData)(_lib_routePath__WEBPACK_IMPORTED_MODULE_8__.legrandMapping_routes.GET_LEGRAND_MAPPINGS);\n            if (response && Array.isArray(response)) {\n                setLegrandData(response);\n            }\n        } catch (error) {\n            sonner__WEBPACK_IMPORTED_MODULE_10__.toast.error(\"Error fetching LEGRAND mapping data:\", error);\n        }\n    }, []);\n    const fetchManualMatchingData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        try {\n            const response = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_7__.getAllData)(_lib_routePath__WEBPACK_IMPORTED_MODULE_8__.manualMatchingMapping_routes.GET_MANUAL_MATCHING_MAPPINGS);\n            if (response && Array.isArray(response)) {\n                setManualMatchingData(response);\n            }\n        } catch (error) {\n            sonner__WEBPACK_IMPORTED_MODULE_10__.toast.error(\"Error fetching manual matching mapping data:\", error);\n        }\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchLegrandData();\n        fetchManualMatchingData();\n    }, [\n        fetchLegrandData,\n        fetchManualMatchingData\n    ]);\n    const handleLegrandDataChange = (entryIndex, businessUnit, divisionCode)=>{\n        form.setValue(\"entries.\".concat(entryIndex, \".company\"), businessUnit);\n        if (divisionCode) {\n            form.setValue(\"entries.\".concat(entryIndex, \".division\"), divisionCode);\n            handleManualMatchingAutoFill(entryIndex, divisionCode);\n        } else {\n            form.setValue(\"entries.\".concat(entryIndex, \".division\"), \"\");\n            form.setValue(\"entries.\".concat(entryIndex, \".manualMatching\"), \"\");\n        }\n    };\n    const handleManualMatchingAutoFill = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((entryIndex, division)=>{\n        var _formValues_entries, _clientOptions_find;\n        if (!division || !manualMatchingData.length) {\n            return;\n        }\n        const formValues = form.getValues();\n        const entry = (_formValues_entries = formValues.entries) === null || _formValues_entries === void 0 ? void 0 : _formValues_entries[entryIndex];\n        const entryClientId = (entry === null || entry === void 0 ? void 0 : entry.clientId) || (entryIndex === 0 ? formValues.clientId : \"\");\n        const entryClientName = (clientOptions === null || clientOptions === void 0 ? void 0 : (_clientOptions_find = clientOptions.find((c)=>c.value === entryClientId)) === null || _clientOptions_find === void 0 ? void 0 : _clientOptions_find.name) || \"\";\n        if (entryClientName !== \"LEGRAND\") {\n            return;\n        }\n        const matchingEntry = manualMatchingData.find((mapping)=>mapping.division === division);\n        if (matchingEntry && matchingEntry.ManualShipment) {\n            form.setValue(\"entries.\".concat(entryIndex, \".manualMatching\"), matchingEntry.ManualShipment);\n        } else {\n            form.setValue(\"entries.\".concat(entryIndex, \".manualMatching\"), \"\");\n        }\n    }, [\n        form,\n        manualMatchingData,\n        clientOptions\n    ]);\n    const fetchCustomFieldsForClient = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (clientId)=>{\n        if (!clientId) return [];\n        try {\n            const allCustomFieldsResponse = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_7__.getAllData)(\"\".concat(_lib_routePath__WEBPACK_IMPORTED_MODULE_8__.clientCustomFields_routes.GET_CLIENT_CUSTOM_FIELDS, \"/\").concat(clientId));\n            let customFieldsData = [];\n            if (allCustomFieldsResponse && allCustomFieldsResponse.custom_fields && allCustomFieldsResponse.custom_fields.length > 0) {\n                customFieldsData = allCustomFieldsResponse.custom_fields.map((field)=>{\n                    let autoFilledValue = \"\";\n                    if (field.type === \"AUTO\") {\n                        if (field.autoOption === \"DATE\") {\n                            autoFilledValue = new Date().toISOString().split(\"T\")[0];\n                        } else if (field.autoOption === \"USERNAME\") {\n                            autoFilledValue = (userData === null || userData === void 0 ? void 0 : userData.username) || \"\";\n                        }\n                    }\n                    return {\n                        id: field.id,\n                        name: field.name,\n                        type: field.type,\n                        autoOption: field.autoOption,\n                        value: autoFilledValue\n                    };\n                });\n            }\n            return customFieldsData;\n        } catch (error) {\n            return [];\n        }\n    }, [\n        userData\n    ]);\n    const { fields, append, remove } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_17__.useFieldArray)({\n        control: form.control,\n        name: \"entries\"\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        companyFieldRefs.current = companyFieldRefs.current.slice(0, fields.length);\n    }, [\n        fields.length\n    ]);\n    const generateFilename = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((entryIndex, formValues)=>{\n        try {\n            const entry = formValues.entries[entryIndex];\n            if (!entry) return {\n                filename: \"\",\n                isValid: false,\n                missing: [\n                    \"Entry data\"\n                ]\n            };\n            const missing = [];\n            const selectedAssociate = associate === null || associate === void 0 ? void 0 : associate.find((a)=>{\n                var _a_id;\n                return ((_a_id = a.id) === null || _a_id === void 0 ? void 0 : _a_id.toString()) === formValues.associateId;\n            });\n            const associateName = (selectedAssociate === null || selectedAssociate === void 0 ? void 0 : selectedAssociate.name) || \"\";\n            if (!associateName) {\n                missing.push(\"Associate\");\n            }\n            const entryClientId = entry.clientId || formValues.clientId;\n            const selectedClient = client === null || client === void 0 ? void 0 : client.find((c)=>{\n                var _c_id;\n                return ((_c_id = c.id) === null || _c_id === void 0 ? void 0 : _c_id.toString()) === entryClientId;\n            });\n            const clientName = (selectedClient === null || selectedClient === void 0 ? void 0 : selectedClient.client_name) || \"\";\n            if (!clientName) {\n                missing.push(\"Client\");\n            }\n            let carrierName = \"\";\n            if (entry.carrierName) {\n                const carrierOption = carrier === null || carrier === void 0 ? void 0 : carrier.find((c)=>{\n                    var _c_id;\n                    return ((_c_id = c.id) === null || _c_id === void 0 ? void 0 : _c_id.toString()) === entry.carrierName;\n                });\n                carrierName = (carrierOption === null || carrierOption === void 0 ? void 0 : carrierOption.name) || \"\";\n            }\n            if (!carrierName) {\n                missing.push(\"Carrier\");\n            }\n            const receivedDate = entry.receivedDate;\n            const invoiceDate = entry.invoiceDate;\n            const currentDate = new Date();\n            const year = currentDate.getFullYear().toString();\n            const month = currentDate.toLocaleString(\"default\", {\n                month: \"short\"\n            }).toUpperCase();\n            if (!invoiceDate) {\n                missing.push(\"Invoice Date\");\n            }\n            let receivedDateStr = \"\";\n            if (receivedDate) {\n                const dateParts = receivedDate.split(\"/\");\n                if (dateParts.length === 3) {\n                    const day = parseInt(dateParts[0], 10);\n                    const month = parseInt(dateParts[1], 10);\n                    const year = parseInt(dateParts[2], 10);\n                    const paddedMonth = month.toString().padStart(2, \"0\");\n                    const paddedDay = day.toString().padStart(2, \"0\");\n                    receivedDateStr = \"\".concat(year, \"-\").concat(paddedMonth, \"-\").concat(paddedDay);\n                } else {\n                    const date = new Date(receivedDate);\n                    receivedDateStr = date.toISOString().split(\"T\")[0];\n                }\n            } else {\n                missing.push(\"Received Date\");\n            }\n            const ftpFileName = entry.ftpFileName || \"\";\n            const baseFilename = ftpFileName ? ftpFileName.endsWith(\".pdf\") ? ftpFileName : \"\".concat(ftpFileName, \".pdf\") : \"\";\n            if (!baseFilename) {\n                missing.push(\"FTP File Name\");\n            }\n            const isValid = missing.length === 0;\n            const filename = isValid ? \"/\".concat(associateName, \"/\").concat(clientName, \"/CARRIERINVOICES/\").concat(carrierName, \"/\").concat(year, \"/\").concat(month, \"/\").concat(receivedDateStr, \"/\").concat(baseFilename) : \"\";\n            return {\n                filename,\n                isValid,\n                missing\n            };\n        } catch (error) {\n            return {\n                filename: \"\",\n                isValid: false,\n                missing: [\n                    \"Error generating filename\"\n                ]\n            };\n        }\n    }, [\n        client,\n        carrier,\n        associate\n    ]);\n    const handleCompanyAutoPopulation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((entryIndex, entryClientId)=>{\n        var _clientOptions_find;\n        const entryClientName = (clientOptions === null || clientOptions === void 0 ? void 0 : (_clientOptions_find = clientOptions.find((c)=>c.value === entryClientId)) === null || _clientOptions_find === void 0 ? void 0 : _clientOptions_find.name) || \"\";\n        const currentEntry = form.getValues(\"entries.\".concat(entryIndex));\n        if (entryClientName && entryClientName !== \"LEGRAND\") {\n            form.setValue(\"entries.\".concat(entryIndex, \".company\"), entryClientName);\n        } else if (entryClientName === \"LEGRAND\") {\n            const shipperAlias = currentEntry.shipperAlias;\n            const consigneeAlias = currentEntry.consigneeAlias;\n            const billtoAlias = currentEntry.billtoAlias;\n            const hasAnyLegrandData = shipperAlias || consigneeAlias || billtoAlias;\n            if (!hasAnyLegrandData && currentEntry.company !== \"\") {\n                form.setValue(\"entries.\".concat(entryIndex, \".company\"), \"\");\n            }\n        } else {\n            if (currentEntry.company !== \"\") {\n                form.setValue(\"entries.\".concat(entryIndex, \".company\"), \"\");\n            }\n        }\n    }, [\n        form,\n        clientOptions\n    ]);\n    const handleCustomFieldsFetch = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (entryIndex, entryClientId)=>{\n        var _currentCustomFields_, _currentCustomFields_1;\n        if (!entryClientId) {\n            const currentCustomFields = form.getValues(\"entries.\".concat(entryIndex, \".customFields\"));\n            if (currentCustomFields && currentCustomFields.length > 0) {\n                form.setValue(\"entries.\".concat(entryIndex, \".customFields\"), []);\n            }\n            return;\n        }\n        const currentCustomFields = form.getValues(\"entries.\".concat(entryIndex, \".customFields\")) || [];\n        const hasEmptyAutoUsernameFields = currentCustomFields.some((field)=>field.type === \"AUTO\" && field.autoOption === \"USERNAME\" && !field.value && (userData === null || userData === void 0 ? void 0 : userData.username));\n        const shouldFetchCustomFields = currentCustomFields.length === 0 || currentCustomFields.length > 0 && !((_currentCustomFields_ = currentCustomFields[0]) === null || _currentCustomFields_ === void 0 ? void 0 : _currentCustomFields_.clientId) || ((_currentCustomFields_1 = currentCustomFields[0]) === null || _currentCustomFields_1 === void 0 ? void 0 : _currentCustomFields_1.clientId) !== entryClientId || hasEmptyAutoUsernameFields;\n        if (shouldFetchCustomFields) {\n            const customFieldsData = await fetchCustomFieldsForClient(entryClientId);\n            const fieldsWithClientId = customFieldsData.map((field)=>({\n                    ...field,\n                    clientId: entryClientId\n                }));\n            form.setValue(\"entries.\".concat(entryIndex, \".customFields\"), fieldsWithClientId);\n            setTimeout(()=>{\n                fieldsWithClientId.forEach((field, fieldIndex)=>{\n                    const fieldPath = \"entries.\".concat(entryIndex, \".customFields.\").concat(fieldIndex, \".value\");\n                    if (field.value) {\n                        form.setValue(fieldPath, field.value);\n                    }\n                });\n                setCustomFieldsRefresh((prev)=>prev + 1);\n            }, 100);\n        }\n    }, [\n        form,\n        fetchCustomFieldsForClient,\n        userData === null || userData === void 0 ? void 0 : userData.username\n    ]);\n    const updateFilenames = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        const formValues = form.getValues();\n        const newFilenames = [];\n        const newValidation = [];\n        const newMissingFields = [];\n        if (formValues.entries && Array.isArray(formValues.entries)) {\n            formValues.entries.forEach((_, index)=>{\n                const { filename, isValid, missing } = generateFilename(index, formValues);\n                newFilenames[index] = filename;\n                newValidation[index] = isValid;\n                newMissingFields[index] = missing || [];\n            });\n        }\n        setGeneratedFilenames(newFilenames);\n        setFilenameValidation(newValidation);\n        setMissingFields(newMissingFields);\n    }, [\n        form,\n        generateFilename\n    ]);\n    const handleInitialSelection = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((associateId, clientId)=>{\n        form.setValue(\"associateId\", associateId);\n        form.setValue(\"clientId\", clientId);\n        setTimeout(()=>{\n            handleCompanyAutoPopulation(0, clientId);\n            handleCustomFieldsFetch(0, clientId);\n            updateFilenames();\n        }, 50);\n        setShowFullForm(true);\n    }, [\n        form,\n        handleCompanyAutoPopulation,\n        handleCustomFieldsFetch,\n        updateFilenames\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const timeoutId = setTimeout(()=>{\n            updateFilenames();\n            const formValues = form.getValues();\n            if (formValues.entries && Array.isArray(formValues.entries)) {\n                formValues.entries.forEach((entry, index)=>{\n                    const entryClientId = (entry === null || entry === void 0 ? void 0 : entry.clientId) || (index === 0 ? formValues.clientId : \"\");\n                    if (entryClientId) {\n                        handleCustomFieldsFetch(index, entryClientId);\n                    }\n                });\n            }\n        }, 50);\n        return ()=>clearTimeout(timeoutId);\n    }, [\n        updateFilenames,\n        handleCustomFieldsFetch,\n        form\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const subscription = form.watch((_, param)=>{\n            let { name } = param;\n            if (name && (name.includes(\"associateId\") || name.includes(\"clientId\") || name.includes(\"carrierName\") || name.includes(\"invoiceDate\") || name.includes(\"receivedDate\") || name.includes(\"ftpFileName\") || name.includes(\"company\") || name.includes(\"division\"))) {\n                updateFilenames();\n                if (name.includes(\"division\")) {\n                    const entryMatch = name.match(/entries\\.(\\d+)\\.division/);\n                    if (entryMatch) {\n                        var _formValues_entries, _clientOptions_find;\n                        const entryIndex = parseInt(entryMatch[1], 10);\n                        const formValues = form.getValues();\n                        const entry = (_formValues_entries = formValues.entries) === null || _formValues_entries === void 0 ? void 0 : _formValues_entries[entryIndex];\n                        const entryClientId = (entry === null || entry === void 0 ? void 0 : entry.clientId) || (entryIndex === 0 ? formValues.clientId : \"\");\n                        const entryClientName = (clientOptions === null || clientOptions === void 0 ? void 0 : (_clientOptions_find = clientOptions.find((c)=>c.value === entryClientId)) === null || _clientOptions_find === void 0 ? void 0 : _clientOptions_find.name) || \"\";\n                        if (entryClientName === \"LEGRAND\") {\n                            var _formValues_entries_entryIndex, _formValues_entries1;\n                            const divisionValue = (_formValues_entries1 = formValues.entries) === null || _formValues_entries1 === void 0 ? void 0 : (_formValues_entries_entryIndex = _formValues_entries1[entryIndex]) === null || _formValues_entries_entryIndex === void 0 ? void 0 : _formValues_entries_entryIndex.division;\n                            if (divisionValue) {\n                                setTimeout(()=>{\n                                    handleManualMatchingAutoFill(entryIndex, divisionValue);\n                                }, 50);\n                            }\n                        }\n                    }\n                }\n                if (name.includes(\"clientId\")) {\n                    const entryMatch = name.match(/entries\\.(\\d+)\\.clientId/);\n                    if (entryMatch) {\n                        const entryIndex = parseInt(entryMatch[1], 10);\n                        setTimeout(()=>{\n                            var _formValues_entries, _clientOptions_find;\n                            const formValues = form.getValues();\n                            const entry = (_formValues_entries = formValues.entries) === null || _formValues_entries === void 0 ? void 0 : _formValues_entries[entryIndex];\n                            const entryClientId = (entry === null || entry === void 0 ? void 0 : entry.clientId) || formValues.clientId || \"\";\n                            const entryClientName = (clientOptions === null || clientOptions === void 0 ? void 0 : (_clientOptions_find = clientOptions.find((c)=>c.value === entryClientId)) === null || _clientOptions_find === void 0 ? void 0 : _clientOptions_find.name) || \"\";\n                            if (entryClientName === \"LEGRAND\" && (entry === null || entry === void 0 ? void 0 : entry.division)) {\n                                handleManualMatchingAutoFill(entryIndex, entry.division);\n                            }\n                        }, 100);\n                    }\n                }\n            }\n        });\n        return ()=>subscription.unsubscribe();\n    }, [\n        form,\n        updateFilenames,\n        handleManualMatchingAutoFill,\n        clientOptions\n    ]);\n    const onSubmit = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (values)=>{\n        try {\n            const currentFormValues = form.getValues();\n            const currentValidation = [];\n            const currentMissingFields = [];\n            const currentFilenames = [];\n            if (currentFormValues.entries && Array.isArray(currentFormValues.entries)) {\n                currentFormValues.entries.forEach((_, index)=>{\n                    const { filename, isValid, missing } = generateFilename(index, currentFormValues);\n                    currentValidation[index] = isValid;\n                    currentMissingFields[index] = missing || [];\n                    currentFilenames[index] = filename;\n                });\n            }\n            const allFilenamesValid = currentValidation.every((isValid)=>isValid);\n            if (!allFilenamesValid) {\n                const invalidEntries = currentValidation.map((isValid, index)=>({\n                        index,\n                        isValid,\n                        missing: currentMissingFields[index]\n                    })).filter((entry)=>!entry.isValid);\n                const errorDetails = invalidEntries.map((entry)=>\"Entry \".concat(entry.index + 1, \": \").concat(entry.missing.join(\", \"))).join(\" | \");\n                sonner__WEBPACK_IMPORTED_MODULE_10__.toast.error(\"Cannot submit: Missing fields - \".concat(errorDetails));\n                return;\n            }\n            const entries = values.entries.map((entry, index)=>{\n                var _entry_customFields;\n                return {\n                    company: entry.company,\n                    division: entry.division,\n                    invoice: entry.invoice,\n                    masterInvoice: entry.masterInvoice,\n                    bol: entry.bol,\n                    invoiceDate: entry.invoiceDate,\n                    receivedDate: entry.receivedDate,\n                    shipmentDate: entry.shipmentDate,\n                    carrierId: entry.carrierName,\n                    invoiceStatus: entry.invoiceStatus,\n                    manualMatching: entry.manualMatching,\n                    invoiceType: entry.invoiceType,\n                    billToClient: entry.billToClient,\n                    currency: entry.currency,\n                    qtyShipped: entry.qtyShipped,\n                    weightUnitName: entry.weightUnitName,\n                    quantityBilledText: entry.quantityBilledText,\n                    invoiceTotal: entry.invoiceTotal,\n                    savings: entry.savings,\n                    ftpFileName: entry.ftpFileName,\n                    ftpPage: entry.ftpPage,\n                    docAvailable: entry.docAvailable,\n                    notes: entry.notes,\n                    //mistake: entry.mistake,\n                    filePath: generatedFilenames[index],\n                    customFields: (_entry_customFields = entry.customFields) === null || _entry_customFields === void 0 ? void 0 : _entry_customFields.map((cf)=>({\n                            id: cf.id,\n                            value: cf.value\n                        }))\n                };\n            });\n            const formData = {\n                clientId: values.clientId,\n                entries: entries\n            };\n            const result = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_7__.formSubmit)(_lib_routePath__WEBPACK_IMPORTED_MODULE_8__.trackSheets_routes.CREATE_TRACK_SHEETS, \"POST\", formData);\n            if (result.success) {\n                sonner__WEBPACK_IMPORTED_MODULE_10__.toast.success(\"All TrackSheets created successfully\");\n                form.reset();\n                setTimeout(()=>{\n                    handleInitialSelection(initialAssociateId, initialClientId);\n                }, 100);\n            } else {\n                sonner__WEBPACK_IMPORTED_MODULE_10__.toast.error(result.message || \"Failed to create TrackSheets\");\n            }\n            router.refresh();\n        } catch (error) {\n            sonner__WEBPACK_IMPORTED_MODULE_10__.toast.error(\"An error occurred while creating the TrackSheets\");\n        }\n    }, [\n        form,\n        router,\n        generateFilename,\n        initialAssociateId,\n        initialClientId,\n        handleInitialSelection,\n        generatedFilenames\n    ]);\n    const addNewEntry = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        const newIndex = fields.length;\n        append({\n            clientId: initialClientId,\n            company: \"\",\n            division: \"\",\n            invoice: \"\",\n            masterInvoice: \"\",\n            bol: \"\",\n            invoiceDate: \"\",\n            receivedDate: \"\",\n            shipmentDate: \"\",\n            carrierName: \"\",\n            invoiceStatus: \"ENTRY\",\n            manualMatching: \"\",\n            invoiceType: \"\",\n            billToClient: \"\",\n            currency: \"\",\n            qtyShipped: \"\",\n            weightUnitName: \"\",\n            quantityBilledText: \"\",\n            invoiceTotal: \"\",\n            savings: \"\",\n            financialNotes: \"\",\n            ftpFileName: \"\",\n            ftpPage: \"\",\n            docAvailable: [],\n            otherDocuments: \"\",\n            notes: \"\",\n            //mistake: \"\",\n            legrandAlias: \"\",\n            legrandCompanyName: \"\",\n            legrandAddress: \"\",\n            legrandZipcode: \"\",\n            shipperAlias: \"\",\n            shipperAddress: \"\",\n            shipperZipcode: \"\",\n            consigneeAlias: \"\",\n            consigneeAddress: \"\",\n            consigneeZipcode: \"\",\n            billtoAlias: \"\",\n            billtoAddress: \"\",\n            billtoZipcode: \"\",\n            customFields: []\n        });\n        setTimeout(()=>{\n            handleCompanyAutoPopulation(newIndex, initialClientId);\n            handleCustomFieldsFetch(newIndex, initialClientId);\n            if (companyFieldRefs.current[newIndex]) {\n                var _companyFieldRefs_current_newIndex, _companyFieldRefs_current_newIndex1, _companyFieldRefs_current_newIndex2;\n                const inputElement = ((_companyFieldRefs_current_newIndex = companyFieldRefs.current[newIndex]) === null || _companyFieldRefs_current_newIndex === void 0 ? void 0 : _companyFieldRefs_current_newIndex.querySelector(\"input\")) || ((_companyFieldRefs_current_newIndex1 = companyFieldRefs.current[newIndex]) === null || _companyFieldRefs_current_newIndex1 === void 0 ? void 0 : _companyFieldRefs_current_newIndex1.querySelector(\"button\")) || ((_companyFieldRefs_current_newIndex2 = companyFieldRefs.current[newIndex]) === null || _companyFieldRefs_current_newIndex2 === void 0 ? void 0 : _companyFieldRefs_current_newIndex2.querySelector(\"select\"));\n                if (inputElement) {\n                    inputElement.focus();\n                    try {\n                        inputElement.click();\n                    } catch (e) {}\n                }\n            }\n            updateFilenames();\n        }, 200);\n    }, [\n        append,\n        fields.length,\n        updateFilenames,\n        initialClientId,\n        handleCompanyAutoPopulation,\n        handleCustomFieldsFetch\n    ]);\n    const handleFormKeyDown = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((e)=>{\n        if (e.ctrlKey && (e.key === \"s\" || e.key === \"S\")) {\n            e.preventDefault();\n            form.handleSubmit(onSubmit)();\n        } else if (e.shiftKey && e.key === \"Enter\") {\n            e.preventDefault();\n            addNewEntry();\n        } else if (e.key === \"Enter\" && !e.ctrlKey && !e.shiftKey && !e.altKey) {\n            const activeElement = document.activeElement;\n            const isSubmitButton = (activeElement === null || activeElement === void 0 ? void 0 : activeElement.getAttribute(\"type\")) === \"submit\";\n            if (isSubmitButton) {\n                e.preventDefault();\n                form.handleSubmit(onSubmit)();\n            }\n        }\n    }, [\n        form,\n        onSubmit,\n        addNewEntry\n    ]);\n    const removeEntry = (index)=>{\n        if (fields.length > 1) {\n            remove(index);\n        } else {\n            sonner__WEBPACK_IMPORTED_MODULE_10__.toast.error(\"You must have at least one entry\");\n        }\n    };\n    const getFilteredDivisionOptions = (company, entryIndex)=>{\n        if (!company || !legrandData.length) {\n            return [];\n        }\n        if (entryIndex !== undefined) {\n            var _formValues_entries, _clientOptions_find;\n            const formValues = form.getValues();\n            const entry = (_formValues_entries = formValues.entries) === null || _formValues_entries === void 0 ? void 0 : _formValues_entries[entryIndex];\n            const entryClientId = (entry === null || entry === void 0 ? void 0 : entry.clientId) || (entryIndex === 0 ? formValues.clientId : \"\");\n            const entryClientName = (clientOptions === null || clientOptions === void 0 ? void 0 : (_clientOptions_find = clientOptions.find((c)=>c.value === entryClientId)) === null || _clientOptions_find === void 0 ? void 0 : _clientOptions_find.name) || \"\";\n            if (entryClientName === \"LEGRAND\") {\n                const shipperAlias = form.getValues(\"entries.\".concat(entryIndex, \".shipperAlias\"));\n                const consigneeAlias = form.getValues(\"entries.\".concat(entryIndex, \".consigneeAlias\"));\n                const billtoAlias = form.getValues(\"entries.\".concat(entryIndex, \".billtoAlias\"));\n                const currentAlias = shipperAlias || consigneeAlias || billtoAlias;\n                if (currentAlias) {\n                    const selectedData = legrandData.find((data)=>{\n                        const uniqueKey = \"\".concat(data.customeCode, \"-\").concat(data.aliasShippingNames || data.legalName, \"-\").concat(data.shippingBillingAddress);\n                        return uniqueKey === currentAlias;\n                    });\n                    if (selectedData) {\n                        const baseAliasName = selectedData.aliasShippingNames && selectedData.aliasShippingNames !== \"NONE\" ? selectedData.aliasShippingNames : selectedData.legalName;\n                        const sameAliasEntries = legrandData.filter((data)=>{\n                            const dataAliasName = data.aliasShippingNames && data.aliasShippingNames !== \"NONE\" ? data.aliasShippingNames : data.legalName;\n                            return dataAliasName === baseAliasName;\n                        });\n                        const allDivisions = [];\n                        sameAliasEntries.forEach((entry)=>{\n                            if (entry.customeCode) {\n                                if (entry.customeCode.includes(\"/\")) {\n                                    const splitDivisions = entry.customeCode.split(\"/\").map((d)=>d.trim());\n                                    allDivisions.push(...splitDivisions);\n                                } else {\n                                    allDivisions.push(entry.customeCode);\n                                }\n                            }\n                        });\n                        const uniqueDivisions = Array.from(new Set(allDivisions.filter((code)=>code)));\n                        if (uniqueDivisions.length > 1) {\n                            const contextDivisions = uniqueDivisions.sort().map((code)=>({\n                                    value: code,\n                                    label: code\n                                }));\n                            return contextDivisions;\n                        } else {}\n                    }\n                }\n            }\n        }\n        const allDivisions = [];\n        legrandData.filter((data)=>data.businessUnit === company && data.customeCode).forEach((data)=>{\n            if (data.customeCode.includes(\"/\")) {\n                const splitDivisions = data.customeCode.split(\"/\").map((d)=>d.trim());\n                allDivisions.push(...splitDivisions);\n            } else {\n                allDivisions.push(data.customeCode);\n            }\n        });\n        const divisions = Array.from(new Set(allDivisions.filter((code)=>code))).sort().map((code)=>({\n                value: code,\n                label: code\n            }));\n        return divisions;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_13__.TooltipProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full px-2 py-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-4 pl-3 mb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                variant: \"default\",\n                                onClick: ()=>setActiveView(\"view\"),\n                                className: \"w-40 shadow-md rounded-xl text-base transition-all duration-200 \".concat(activeView === \"view\" ? \"bg-neutral-800 hover:bg-neutral-900 text-white\" : \"bg-white text-neutral-800 border border-neutral-300 hover:bg-neutral-100\"),\n                                children: \"View TrackSheet\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                lineNumber: 1116,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                variant: \"default\",\n                                onClick: ()=>setActiveView(\"create\"),\n                                className: \"w-40 shadow-md rounded-xl text-base transition-all duration-200 \".concat(activeView === \"create\" ? \"bg-neutral-800 hover:bg-neutral-900 text-white\" : \"bg-white text-neutral-800 border border-neutral-300 hover:bg-neutral-100\"),\n                                children: \"Create TrackSheet\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                lineNumber: 1127,\n                                columnNumber: 13\n                            }, undefined),\n                            activeView === \"create\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-2 ml-4 flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"w-4 h-4 text-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                lineNumber: 1143,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-sm font-semibold text-gray-900\",\n                                                children: \"Create TrackSheet\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                lineNumber: 1144,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                        lineNumber: 1142,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_3__.Form, {\n                                        ...selectionForm,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        form: selectionForm,\n                                                        name: \"associateId\",\n                                                        label: \"Select Associate\",\n                                                        placeholder: \"Search Associate...\",\n                                                        isRequired: true,\n                                                        options: associateOptions || [],\n                                                        onValueChange: (value)=>{\n                                                            setInitialAssociateId(value);\n                                                            if (value && initialClientId) {\n                                                                validateClientForAssociate(value, initialClientId);\n                                                            } else {\n                                                                setInitialClientId(\"\");\n                                                                selectionForm.setValue(\"clientId\", \"\");\n                                                            }\n                                                            setShowFullForm(false);\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                        lineNumber: 1152,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                    lineNumber: 1151,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        form: selectionForm,\n                                                        name: \"clientId\",\n                                                        label: \"Select Client\",\n                                                        placeholder: \"Search Client...\",\n                                                        isRequired: true,\n                                                        disabled: !initialAssociateId,\n                                                        options: clientOptions || [],\n                                                        onValueChange: (value)=>{\n                                                            setInitialClientId(value);\n                                                            if (showFullForm) {\n                                                                form.reset();\n                                                                clearEntrySpecificClients();\n                                                            }\n                                                            if (value && initialAssociateId) {\n                                                                setTimeout(()=>{\n                                                                    handleInitialSelection(initialAssociateId, value);\n                                                                }, 100);\n                                                            } else {\n                                                                setShowFullForm(false);\n                                                            }\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                        lineNumber: 1174,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                    lineNumber: 1173,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                            lineNumber: 1150,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                        lineNumber: 1149,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                lineNumber: 1141,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                        lineNumber: 1115,\n                        columnNumber: 11\n                    }, undefined),\n                    activeView === \"view\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full animate-in fade-in duration-500 rounded-2xl shadow-sm dark:bg-gray-800 p-1\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ClientSelectPage__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            permissions: permissions,\n                            client: client,\n                            clientDataUpdate: clientDataUpdate,\n                            carrierDataUpdate: carrierDataUpdate\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                            lineNumber: 1209,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                        lineNumber: 1208,\n                        columnNumber: 13\n                    }, undefined) : /* Form Section - Only show when both associate and client are selected */ showFullForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_3__.Form, {\n                        ...form,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: form.handleSubmit(onSubmit),\n                            onKeyDown: handleFormKeyDown,\n                            className: \"space-y-3\",\n                            children: [\n                                fields.map((field, index)=>{\n                                    var _missingFields_index;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-2 bg-gray-100 rounded-md px-3 py-2 border border-gray-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-5 h-5 bg-gray-600 rounded-full flex items-center justify-center text-white font-semibold text-xs\",\n                                                            children: index + 1\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                            lineNumber: 1225,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                            className: \"text-sm font-semibold text-gray-900\",\n                                                            children: [\n                                                                \"Entry #\",\n                                                                index + 1\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                            lineNumber: 1228,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                    lineNumber: 1224,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                lineNumber: 1223,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-3 pb-3 border-b border-gray-100\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-gray-50 rounded-md p-2 mb-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-2 mb-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                            className: \"w-4 h-4 text-blue-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1241,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"text-sm font-semibold text-gray-900\",\n                                                                            children: \"Client Information\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1242,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1240,\n                                                                    columnNumber: 29\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2 mb-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                                className: \"mt-2\",\n                                                                                form: form,\n                                                                                label: \"FTP File Name\",\n                                                                                name: \"entries.\".concat(index, \".ftpFileName\"),\n                                                                                type: \"text\",\n                                                                                isRequired: true\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                lineNumber: 1249,\n                                                                                columnNumber: 33\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1248,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_PageInput__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                            className: \"mt-2\",\n                                                                            form: form,\n                                                                            label: \"FTP Page\",\n                                                                            name: \"entries.\".concat(index, \".ftpPage\"),\n                                                                            isRequired: true\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1258,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex flex-col\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                className: \"mt-0\",\n                                                                                form: form,\n                                                                                name: \"entries.\".concat(index, \".carrierName\"),\n                                                                                label: \"Select Carrier\",\n                                                                                placeholder: \"Search Carrier\",\n                                                                                isRequired: true,\n                                                                                options: (carrierOptions === null || carrierOptions === void 0 ? void 0 : carrierOptions.filter((carrier)=>{\n                                                                                    const currentEntries = form.getValues(\"entries\") || [];\n                                                                                    // const isSelectedInOtherEntries =\n                                                                                    //   currentEntries.some(\n                                                                                    //     (entry: any, entryIndex: number) =>\n                                                                                    //       entryIndex !== index &&\n                                                                                    //       entry.carrierName === carrier.value\n                                                                                    //   );\n                                                                                    return !isSelectedInOtherEntries;\n                                                                                })) || [],\n                                                                                onValueChange: ()=>{\n                                                                                    setTimeout(()=>updateFilenames(), 100);\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                lineNumber: 1266,\n                                                                                columnNumber: 33\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1265,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex flex-col\",\n                                                                            children: (()=>{\n                                                                                var _formValues_entries, _clientOptions_find;\n                                                                                const formValues = form.getValues();\n                                                                                const entry = (_formValues_entries = formValues.entries) === null || _formValues_entries === void 0 ? void 0 : _formValues_entries[index];\n                                                                                const entryClientId = (entry === null || entry === void 0 ? void 0 : entry.clientId) || formValues.clientId || \"\";\n                                                                                const entryClientName = (clientOptions === null || clientOptions === void 0 ? void 0 : (_clientOptions_find = clientOptions.find((c)=>c.value === entryClientId)) === null || _clientOptions_find === void 0 ? void 0 : _clientOptions_find.name) || \"\";\n                                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"mt-6\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                            className: \"text-sm font-medium text-gray-700 mb-1 block\",\n                                                                                            children: [\n                                                                                                \"Billed to \",\n                                                                                                entryClientName || \"Client\"\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                            lineNumber: 1308,\n                                                                                            columnNumber: 39\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center space-x-4\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                                    className: \"flex items-center\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                                            type: \"radio\",\n                                                                                                            ...form.register(\"entries.\".concat(index, \".billToClient\")),\n                                                                                                            value: \"yes\",\n                                                                                                            className: \"mr-2\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                                            lineNumber: 1313,\n                                                                                                            columnNumber: 43\n                                                                                                        }, undefined),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                            className: \"text-sm\",\n                                                                                                            children: \"Yes\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                                            lineNumber: 1319,\n                                                                                                            columnNumber: 43\n                                                                                                        }, undefined)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                                    lineNumber: 1312,\n                                                                                                    columnNumber: 41\n                                                                                                }, undefined),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                                    className: \"flex items-center\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                                            type: \"radio\",\n                                                                                                            ...form.register(\"entries.\".concat(index, \".billToClient\")),\n                                                                                                            value: \"no\",\n                                                                                                            className: \"mr-2\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                                            lineNumber: 1322,\n                                                                                                            columnNumber: 43\n                                                                                                        }, undefined),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                            className: \"text-sm\",\n                                                                                                            children: \"No\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                                            lineNumber: 1328,\n                                                                                                            columnNumber: 43\n                                                                                                        }, undefined)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                                    lineNumber: 1321,\n                                                                                                    columnNumber: 41\n                                                                                                }, undefined)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                            lineNumber: 1311,\n                                                                                            columnNumber: 39\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                    lineNumber: 1307,\n                                                                                    columnNumber: 37\n                                                                                }, undefined);\n                                                                            })()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1291,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1247,\n                                                                    columnNumber: 29\n                                                                }, undefined),\n                                                                (()=>{\n                                                                    var _formValues_entries, _clientOptions_find;\n                                                                    const formValues = form.getValues();\n                                                                    const entry = (_formValues_entries = formValues.entries) === null || _formValues_entries === void 0 ? void 0 : _formValues_entries[index];\n                                                                    const entryClientId = (entry === null || entry === void 0 ? void 0 : entry.clientId) || formValues.clientId || \"\";\n                                                                    const entryClientName = (clientOptions === null || clientOptions === void 0 ? void 0 : (_clientOptions_find = clientOptions.find((c)=>c.value === entryClientId)) === null || _clientOptions_find === void 0 ? void 0 : _clientOptions_find.name) || \"\";\n                                                                    return entryClientName === \"LEGRAND\";\n                                                                })() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mb-3\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"grid grid-cols-1 lg:grid-cols-3 gap-3 mb-3\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LegrandDetailsComponent__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                form: form,\n                                                                                entryIndex: index,\n                                                                                onLegrandDataChange: handleLegrandDataChange,\n                                                                                blockTitle: \"Shipper\",\n                                                                                fieldPrefix: \"shipper\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                lineNumber: 1351,\n                                                                                columnNumber: 35\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LegrandDetailsComponent__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                form: form,\n                                                                                entryIndex: index,\n                                                                                onLegrandDataChange: handleLegrandDataChange,\n                                                                                blockTitle: \"Consignee\",\n                                                                                fieldPrefix: \"consignee\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                lineNumber: 1360,\n                                                                                columnNumber: 35\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LegrandDetailsComponent__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                form: form,\n                                                                                entryIndex: index,\n                                                                                onLegrandDataChange: handleLegrandDataChange,\n                                                                                blockTitle: \"Bill-to\",\n                                                                                fieldPrefix: \"billto\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                lineNumber: 1369,\n                                                                                columnNumber: 35\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1350,\n                                                                        columnNumber: 33\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1349,\n                                                                    columnNumber: 31\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            ref: (el)=>{\n                                                                                companyFieldRefs.current[index] = el;\n                                                                            },\n                                                                            className: \"flex flex-col mb-1 [&_input]:h-10\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                                form: form,\n                                                                                label: \"Company\",\n                                                                                name: \"entries.\".concat(index, \".company\"),\n                                                                                type: \"text\",\n                                                                                disable: (()=>{\n                                                                                    var _formValues_entries, _clientOptions_find;\n                                                                                    const formValues = form.getValues();\n                                                                                    const entry = (_formValues_entries = formValues.entries) === null || _formValues_entries === void 0 ? void 0 : _formValues_entries[index];\n                                                                                    const entryClientId = (entry === null || entry === void 0 ? void 0 : entry.clientId) || formValues.clientId || \"\";\n                                                                                    const entryClientName = (clientOptions === null || clientOptions === void 0 ? void 0 : (_clientOptions_find = clientOptions.find((c)=>c.value === entryClientId)) === null || _clientOptions_find === void 0 ? void 0 : _clientOptions_find.name) || \"\";\n                                                                                    return entryClientName === \"LEGRAND\";\n                                                                                })()\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                lineNumber: 1390,\n                                                                                columnNumber: 33\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1384,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex flex-col\",\n                                                                            children: (()=>{\n                                                                                var _formValues_entries, _clientOptions_find, _entries_index;\n                                                                                const formValues = form.getValues();\n                                                                                const entry = (_formValues_entries = formValues.entries) === null || _formValues_entries === void 0 ? void 0 : _formValues_entries[index];\n                                                                                const entryClientId = (entry === null || entry === void 0 ? void 0 : entry.clientId) || formValues.clientId || \"\";\n                                                                                const entryClientName = (clientOptions === null || clientOptions === void 0 ? void 0 : (_clientOptions_find = clientOptions.find((c)=>c.value === entryClientId)) === null || _clientOptions_find === void 0 ? void 0 : _clientOptions_find.name) || \"\";\n                                                                                const isLegrand = entryClientName === \"LEGRAND\";\n                                                                                return isLegrand ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                    form: form,\n                                                                                    name: \"entries.\".concat(index, \".division\"),\n                                                                                    label: \"Division\",\n                                                                                    placeholder: \"Search Division\",\n                                                                                    disabled: false,\n                                                                                    options: getFilteredDivisionOptions((entries === null || entries === void 0 ? void 0 : (_entries_index = entries[index]) === null || _entries_index === void 0 ? void 0 : _entries_index.company) || \"\", index),\n                                                                                    onValueChange: (value)=>{\n                                                                                        setTimeout(()=>{\n                                                                                            handleManualMatchingAutoFill(index, value);\n                                                                                        }, 10);\n                                                                                    }\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                    lineNumber: 1430,\n                                                                                    columnNumber: 37\n                                                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                                    form: form,\n                                                                                    label: \"Division\",\n                                                                                    name: \"entries.\".concat(index, \".division\"),\n                                                                                    type: \"text\",\n                                                                                    placeholder: \"Enter Division\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                    lineNumber: 1450,\n                                                                                    columnNumber: 37\n                                                                                }, undefined);\n                                                                            })()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1412,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1383,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                            lineNumber: 1239,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                        lineNumber: 1237,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-3 pb-3 border-b border-gray-100\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                        className: \"w-4 h-4 text-orange-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1467,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"text-sm font-semibold text-gray-900\",\n                                                                        children: \"Document Information\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1468,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                lineNumber: 1466,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-3 gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"Master Invoice\",\n                                                                        name: \"entries.\".concat(index, \".masterInvoice\"),\n                                                                        type: \"text\",\n                                                                        onBlur: (e)=>{\n                                                                            const masterInvoiceValue = e.target.value;\n                                                                            if (masterInvoiceValue) {\n                                                                                form.setValue(\"entries.\".concat(index, \".invoice\"), masterInvoiceValue);\n                                                                            }\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1473,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"Invoice\",\n                                                                        name: \"entries.\".concat(index, \".invoice\"),\n                                                                        type: \"text\",\n                                                                        isRequired: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1488,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"BOL\",\n                                                                        name: \"entries.\".concat(index, \".bol\"),\n                                                                        type: \"text\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1495,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                lineNumber: 1472,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-3 gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"Received Date\",\n                                                                        name: \"entries.\".concat(index, \".receivedDate\"),\n                                                                        type: \"text\",\n                                                                        isRequired: true,\n                                                                        placeholder: \"DD/MM/YYYY\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1503,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"Invoice Date\",\n                                                                        name: \"entries.\".concat(index, \".invoiceDate\"),\n                                                                        type: \"text\",\n                                                                        isRequired: true,\n                                                                        placeholder: \"DD/MM/YYYY\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1511,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"Shipment Date\",\n                                                                        name: \"entries.\".concat(index, \".shipmentDate\"),\n                                                                        type: \"text\",\n                                                                        placeholder: \"DD/MM/YYYY\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1519,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                lineNumber: 1502,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                        lineNumber: 1465,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-4 pb-4 border-b border-gray-100\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 mb-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                        className: \"w-4 h-4 text-green-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1532,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"text-sm font-semibold text-gray-900\",\n                                                                        children: \"Financial & Shipment\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1533,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                lineNumber: 1531,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"Invoice Total\",\n                                                                        name: \"entries.\".concat(index, \".invoiceTotal\"),\n                                                                        type: \"number\",\n                                                                        isRequired: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1538,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        form: form,\n                                                                        name: \"entries.\".concat(index, \".currency\"),\n                                                                        label: \"Currency\",\n                                                                        placeholder: \"Search currency\",\n                                                                        isRequired: true,\n                                                                        options: [\n                                                                            {\n                                                                                value: \"USD\",\n                                                                                label: \"USD\"\n                                                                            },\n                                                                            {\n                                                                                value: \"CAD\",\n                                                                                label: \"CAD\"\n                                                                            },\n                                                                            {\n                                                                                value: \"EUR\",\n                                                                                label: \"EUR\"\n                                                                            }\n                                                                        ]\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1545,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"Savings\",\n                                                                        name: \"entries.\".concat(index, \".savings\"),\n                                                                        type: \"text\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1557,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"Notes\",\n                                                                        name: \"entries.\".concat(index, \".financialNotes\"),\n                                                                        type: \"text\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1563,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                lineNumber: 1537,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2 mt-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"Quantity Shipped\",\n                                                                        name: \"entries.\".concat(index, \".qtyShipped\"),\n                                                                        type: \"number\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1571,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"Weight Unit\",\n                                                                        name: \"entries.\".concat(index, \".weightUnitName\"),\n                                                                        type: \"text\",\n                                                                        isRequired: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1577,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        form: form,\n                                                                        name: \"entries.\".concat(index, \".invoiceType\"),\n                                                                        label: \"Invoice Type\",\n                                                                        placeholder: \"Search Invoice Type\",\n                                                                        isRequired: true,\n                                                                        options: [\n                                                                            {\n                                                                                value: \"FREIGHT\",\n                                                                                label: \"FREIGHT\"\n                                                                            },\n                                                                            {\n                                                                                value: \"ADDITIONAL\",\n                                                                                label: \"ADDITIONAL\"\n                                                                            },\n                                                                            {\n                                                                                value: \"BALANCED DUE\",\n                                                                                label: \"BALANCED DUE\"\n                                                                            },\n                                                                            {\n                                                                                value: \"CREDIT\",\n                                                                                label: \"CREDIT\"\n                                                                            },\n                                                                            {\n                                                                                value: \"REVISED\",\n                                                                                label: \"REVISED\"\n                                                                            }\n                                                                        ]\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1584,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"Quantity Billed Text\",\n                                                                        name: \"entries.\".concat(index, \".quantityBilledText\"),\n                                                                        type: \"text\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1601,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                lineNumber: 1570,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2 mt-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"Invoice Status\",\n                                                                        name: \"entries.\".concat(index, \".invoiceStatus\"),\n                                                                        type: \"text\",\n                                                                        disable: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1609,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1616,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1617,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1618,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                lineNumber: 1608,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                        lineNumber: 1530,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                        className: \"w-4 h-4 text-gray-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1625,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"text-sm font-semibold text-gray-900\",\n                                                                        children: \"Additional Information\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1626,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                lineNumber: 1624,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2\",\n                                                                children: [\n                                                                    (()=>{\n                                                                        var _formValues_entries, _clientOptions_find;\n                                                                        const formValues = form.getValues();\n                                                                        const entry = (_formValues_entries = formValues.entries) === null || _formValues_entries === void 0 ? void 0 : _formValues_entries[index];\n                                                                        const entryClientId = (entry === null || entry === void 0 ? void 0 : entry.clientId) || formValues.clientId || \"\";\n                                                                        const entryClientName = (clientOptions === null || clientOptions === void 0 ? void 0 : (_clientOptions_find = clientOptions.find((c)=>c.value === entryClientId)) === null || _clientOptions_find === void 0 ? void 0 : _clientOptions_find.name) || \"\";\n                                                                        const isLegrand = entryClientName === \"LEGRAND\";\n                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                            form: form,\n                                                                            label: isLegrand ? \"Manual or Matching\" : \"Manual or Matching\",\n                                                                            name: \"entries.\".concat(index, \".manualMatching\"),\n                                                                            type: \"text\",\n                                                                            isRequired: true,\n                                                                            disable: isLegrand,\n                                                                            placeholder: isLegrand ? \"Auto-filled based on division\" : \"Enter manual or matching\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1643,\n                                                                            columnNumber: 33\n                                                                        }, undefined);\n                                                                    })(),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"Notes (Remarks)\",\n                                                                        name: \"entries.\".concat(index, \".notes\"),\n                                                                        type: \"text\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1662,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormCheckboxGroup__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                            form: form,\n                                                                            label: \"Documents Available\",\n                                                                            name: \"entries.\".concat(index, \".docAvailable\"),\n                                                                            options: [\n                                                                                {\n                                                                                    label: \"Invoice\",\n                                                                                    value: \"Invoice\"\n                                                                                },\n                                                                                {\n                                                                                    label: \"BOL\",\n                                                                                    value: \"Bol\"\n                                                                                },\n                                                                                {\n                                                                                    label: \"POD\",\n                                                                                    value: \"Pod\"\n                                                                                },\n                                                                                {\n                                                                                    label: \"Packages List\",\n                                                                                    value: \"Packages List\"\n                                                                                },\n                                                                                {\n                                                                                    label: \"Other Documents\",\n                                                                                    value: \"Other Documents\"\n                                                                                }\n                                                                            ],\n                                                                            className: \"flex-row gap-2 text-xs\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1669,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1668,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    (()=>{\n                                                                        var _formValues_entries;\n                                                                        const formValues = form.getValues();\n                                                                        const entry = (_formValues_entries = formValues.entries) === null || _formValues_entries === void 0 ? void 0 : _formValues_entries[index];\n                                                                        const docAvailable = (entry === null || entry === void 0 ? void 0 : entry.docAvailable) || [];\n                                                                        const hasOtherDocuments = docAvailable.includes(\"Other Documents\");\n                                                                        return hasOtherDocuments ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                            form: form,\n                                                                            label: \"Specify Other Documents\",\n                                                                            name: \"entries.\".concat(index, \".otherDocuments\"),\n                                                                            type: \"text\",\n                                                                            isRequired: true,\n                                                                            placeholder: \"Enter other document types...\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1698,\n                                                                            columnNumber: 33\n                                                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1707,\n                                                                            columnNumber: 33\n                                                                        }, undefined);\n                                                                    })()\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                lineNumber: 1630,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                        lineNumber: 1623,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    (()=>{\n                                                        var _formValues_entries;\n                                                        const formValues = form.getValues();\n                                                        const entry = (_formValues_entries = formValues.entries) === null || _formValues_entries === void 0 ? void 0 : _formValues_entries[index];\n                                                        const customFields = (entry === null || entry === void 0 ? void 0 : entry.customFields) || [];\n                                                        return Array.isArray(customFields) && customFields.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"pt-3 border-t border-gray-100\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-2 mb-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            className: \"w-4 h-4 text-purple-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1733,\n                                                                            columnNumber: 33\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"text-sm font-semibold text-gray-900\",\n                                                                            children: [\n                                                                                \"Custom Fields (\",\n                                                                                customFields.length,\n                                                                                \")\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1734,\n                                                                            columnNumber: 33\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1732,\n                                                                    columnNumber: 31\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2\",\n                                                                    children: customFields.map((cf, cfIdx)=>{\n                                                                        const fieldType = cf.type || \"TEXT\";\n                                                                        const isAutoField = fieldType === \"AUTO\";\n                                                                        const autoOption = cf.autoOption;\n                                                                        let inputType = \"text\";\n                                                                        if (fieldType === \"DATE\" || isAutoField && autoOption === \"DATE\") {\n                                                                            inputType = \"date\";\n                                                                        } else if (fieldType === \"NUMBER\") {\n                                                                            inputType = \"number\";\n                                                                        }\n                                                                        const fieldLabel = isAutoField ? \"\".concat(cf.name, \" (Auto - \").concat(autoOption, \")\") : cf.name;\n                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                            form: form,\n                                                                            label: fieldLabel,\n                                                                            name: \"entries.\".concat(index, \".customFields.\").concat(cfIdx, \".value\"),\n                                                                            type: inputType,\n                                                                            className: \"w-full\",\n                                                                            disable: isAutoField\n                                                                        }, cf.id, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1759,\n                                                                            columnNumber: 37\n                                                                        }, undefined);\n                                                                    })\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1738,\n                                                                    columnNumber: 31\n                                                                }, undefined)\n                                                            ]\n                                                        }, \"custom-fields-\".concat(index, \"-\").concat(customFieldsRefresh), true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                            lineNumber: 1728,\n                                                            columnNumber: 29\n                                                        }, undefined) : null;\n                                                    })(),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"pt-3 border-t border-gray-100 mt-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-end space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_13__.Tooltip, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_13__.TooltipTrigger, {\n                                                                            asChild: true,\n                                                                            tabIndex: -1,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"w-5 h-5 rounded-full flex items-center justify-center text-white font-bold text-xs cursor-help transition-colors duration-200 \".concat(filenameValidation[index] ? \"bg-green-500 hover:bg-green-600\" : \"bg-orange-500 hover:bg-orange-600\"),\n                                                                                tabIndex: -1,\n                                                                                role: \"button\",\n                                                                                \"aria-label\": \"Entry \".concat(index + 1, \" filename status\"),\n                                                                                children: \"!\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                lineNumber: 1781,\n                                                                                columnNumber: 33\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1780,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_13__.TooltipContent, {\n                                                                            side: \"top\",\n                                                                            align: \"center\",\n                                                                            className: \"z-[9999]\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-sm max-w-md\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"font-medium mb-1\",\n                                                                                        children: [\n                                                                                            \"Entry #\",\n                                                                                            index + 1,\n                                                                                            \" Filename\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                        lineNumber: 1802,\n                                                                                        columnNumber: 35\n                                                                                    }, undefined),\n                                                                                    filenameValidation[index] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                className: \"font-medium text-green-600 mb-2\",\n                                                                                                children: \"Filename Generated\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                                lineNumber: 1807,\n                                                                                                columnNumber: 39\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                className: \"text-xs font-mono break-all bg-gray-100 p-2 rounded text-black\",\n                                                                                                children: generatedFilenames[index]\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                                lineNumber: 1810,\n                                                                                                columnNumber: 39\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                        lineNumber: 1806,\n                                                                                        columnNumber: 37\n                                                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                className: \"font-medium text-orange-600 mb-1\",\n                                                                                                children: \"Please fill the form to generate filename\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                                lineNumber: 1816,\n                                                                                                columnNumber: 39\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                className: \"text-xs text-gray-600 mb-2\",\n                                                                                                children: \"Missing fields:\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                                lineNumber: 1820,\n                                                                                                columnNumber: 39\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                                                className: \"list-disc list-inside space-y-1\",\n                                                                                                children: (_missingFields_index = missingFields[index]) === null || _missingFields_index === void 0 ? void 0 : _missingFields_index.map((field, fieldIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                                        className: \"text-xs\",\n                                                                                                        children: field\n                                                                                                    }, fieldIndex, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                                        lineNumber: 1826,\n                                                                                                        columnNumber: 45\n                                                                                                    }, undefined))\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                                lineNumber: 1823,\n                                                                                                columnNumber: 39\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                        lineNumber: 1815,\n                                                                                        columnNumber: 37\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                lineNumber: 1801,\n                                                                                columnNumber: 33\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1796,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1779,\n                                                                    columnNumber: 29\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                                    type: \"button\",\n                                                                    variant: \"outline\",\n                                                                    size: \"sm\",\n                                                                    className: \"h-7 w-7 p-0 hover:bg-red-50 hover:border-red-200\",\n                                                                    onClick: ()=>removeEntry(index),\n                                                                    disabled: fields.length <= 1,\n                                                                    tabIndex: -1,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                        className: \"h-3 w-3 text-red-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1850,\n                                                                        columnNumber: 31\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1841,\n                                                                    columnNumber: 29\n                                                                }, undefined),\n                                                                index === fields.length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_13__.Tooltip, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_13__.TooltipTrigger, {\n                                                                            asChild: true,\n                                                                            tabIndex: -1,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                                                type: \"button\",\n                                                                                variant: \"outline\",\n                                                                                size: \"sm\",\n                                                                                className: \"h-7 w-7 p-0 hover:bg-green-50 hover:border-green-200\",\n                                                                                onClick: addNewEntry,\n                                                                                tabIndex: -1,\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                    className: \"h-3 w-3 text-green-500\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                    lineNumber: 1863,\n                                                                                    columnNumber: 37\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                lineNumber: 1855,\n                                                                                columnNumber: 35\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1854,\n                                                                            columnNumber: 33\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_13__.TooltipContent, {\n                                                                            side: \"top\",\n                                                                            align: \"center\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-xs\",\n                                                                                children: \"Add New Entry (Shift+Enter)\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                lineNumber: 1867,\n                                                                                columnNumber: 35\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1866,\n                                                                            columnNumber: 33\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1853,\n                                                                    columnNumber: 31\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                            lineNumber: 1777,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                        lineNumber: 1776,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                lineNumber: 1235,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        ]\n                                    }, field.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                        lineNumber: 1221,\n                                        columnNumber: 21\n                                    }, undefined);\n                                }),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                            type: \"submit\",\n                                            className: \"px-6 py-2 rounded-lg font-medium transition-all duration-200 shadow-md hover:shadow-lg text-sm\",\n                                            children: \"Save\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                            lineNumber: 1882,\n                                            columnNumber: 23\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                        lineNumber: 1881,\n                                        columnNumber: 21\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                    lineNumber: 1880,\n                                    columnNumber: 19\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                            lineNumber: 1215,\n                            columnNumber: 17\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                        lineNumber: 1214,\n                        columnNumber: 15\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                lineNumber: 1113,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n            lineNumber: 1112,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n        lineNumber: 1111,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CreateTrackSheet, \"uZMLHlCbMWae99cMX7u66tjCGog=\", false, function() {\n    return [\n        react_hook_form__WEBPACK_IMPORTED_MODULE_17__.useForm,\n        next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_17__.useForm,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_17__.useWatch,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_17__.useFieldArray\n    ];\n});\n_c = CreateTrackSheet;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CreateTrackSheet);\nvar _c;\n$RefreshReg$(_c, \"CreateTrackSheet\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/user/trackSheets/createTrackSheet.tsx\n"));

/***/ })

});